/* 页面布局 CSS 变量 */
:root {
  color-scheme: light;

  /* 头部高度 */
  --g-header-height: 60px;

  /* 侧边栏宽度 */
  --g-main-sidebar-width: 80px;
  --g-sub-sidebar-width: 220px;
  --g-sub-sidebar-collapse-width: 64px;

  /* 侧边栏 Logo 区域高度 */
  --g-sidebar-logo-height: 50px;

  /* 标签栏高度 */
  --g-tabbar-height: 50px;

  /* 工具栏高度 */
  --g-toolbar-height: 50px;

  /* 滚动条颜色 */
  --scrollbar-color: 240 5.9% 90%;

  &.dark {
    color-scheme: dark;

    --scrollbar-color: 240 3.7% 15.9%;
  }
}

/* 明暗模式 CSS 变量 */
::view-transition-old(root),
::view-transition-new(root) {
  mix-blend-mode: normal;
  animation: none;
}

::view-transition-old(root) {
  z-index: 0;
}

::view-transition-new(root) {
  z-index: 1;
}

.dark {
  &::view-transition-old(root) {
    z-index: 1;
  }

  &::view-transition-new(root) {
    z-index: 0;
  }
}

* {
  scrollbar-color: hsl(var(--scrollbar-color)) transparent;
  scrollbar-width: thin;
}

html,
body {
  height: 100%;
}

body {
  box-sizing: border-box;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
}

#app {
  height: 100%;
}

/* textarea 字体跟随系统 */
textarea {
  font-family: inherit;
}
