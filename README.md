<p align="center">
  <img src="https://fantastic-admin.hurui.me/logo.svg" width="200" height="200" />
</p>

<h1 align="center">Fantastic-admin</h1>

<p align="center">一款<b>开箱即用</b>的 Vue3 中后台管理系统框架</p>

<p align="center">
  <a href="https://fantastic-admin.hurui.me" target="_blank">官网</a>
  <span>&nbsp;|&nbsp;</span>
  <a href="https://fantastic-admin.pages.dev" target="_blank">备用地址</a>
<p>

<p align="center">
  <a href="###"><img src="https://img.shields.io/github/license/fantastic-admin/basic?label=%E5%BC%80%E6%BA%90%E5%8D%8F%E8%AE%AE&style=flat-square" /></a>
  <a href="https://github.com/fantastic-admin/basic/releases" target="_blank"><img src="https://img.shields.io/github/v/release/fantastic-admin/basic?label=%E5%BD%93%E5%89%8D%E7%89%88%E6%9C%AC&style=flat-square" /></a>
</p>

## 特点

- 可自由替换 UI 组件库，默认使用 Element Plus
- 丰富的布局与主题，覆盖市面上各种中后台应用场景，兼容PC、平板和移动端
- 提供系统配置文件，轻松实现个性化定制
- 根据路由配置自动生成导航栏
- 基于文件系统的路由
- 支持全方位权限验证
- 内置多级路由最佳缓存方案
- 轻松实现国际化多语言适配
- 提供接近于浏览器原生标签栏操作体验的标签页功能

## 下载

> 本仓库为基础版

**直接拉取源码可能会包含未发布的内容，推荐去 [Github Releases](https://github.com/fantastic-admin/basic/releases) 页面下载稳定版本的压缩包**。如果确定需要拉取源码，请参考下列分支说明：

- `main` Vue3 版本框架源码分支，不含示例代码，可直接用于实际开发
- `example` Vue3 版本演示源码分支，同线上演示站，包含大量示例，可用于参考学习
- ~~`vue2` Vue2 版本框架源码分支，不含示例代码，可直接用于实际开发~~（停止维护）
- ~~`vue2-example` Vue2 版本演示源码分支，包含大量示例，可用于参考学习~~（停止维护）

## 预览

> 预览截图为 Vue3 专业版

<table>
  <tr>
    <td><img src="https://fantastic-admin.hurui.me/preview1.png" /></td>
    <td><img src="https://fantastic-admin.hurui.me/preview2.png" /></td>
    <td><img src="https://fantastic-admin.hurui.me/preview3.png" /></td>
  </tr>
  <tr>
    <td><img src="https://fantastic-admin.hurui.me/preview4.png" /></td>
    <td><img src="https://fantastic-admin.hurui.me/preview5.png" /></td>
    <td><img src="https://fantastic-admin.hurui.me/preview6.png" /></td>
  </tr>
</table>

## 支持

如果觉得 Fantastic-admin 这个框架不错，或者已经在使用了，希望你可以在 **Github** 或者 **Gitee(码云)** 帮我点个 ⭐ ，这将对我是极大的鼓励。

[![star](https://img.shields.io/github/stars/fantastic-admin/basic?style=social)](https://github.com/fantastic-admin/basic)

[![star](https://gitee.com/fantastic-admin/basic/badge/star.svg?theme=dark)](https://gitee.com/fantastic-admin/basic)

<details>
<summary>Github Stars 曲线</summary>

[![Stargazers over time](https://starchart.cc/fantastic-admin/basic.svg)](https://starchart.cc/fantastic-admin/basic)
</details>

## 生态

<table>
  <tr>
    <th colspan="3" align="center">
      <a href="https://hooray.github.io/fantastic-startkit/" target="_blank">Fantastic-startkit</a>
    </th>
  </tr>
  <tr>
    <th colspan="3" align="center">
      一款简单好用的 Vue3 项目启动套件
    </th>
  </tr>
</table>

<table>
  <tr>
    <th colspan="3" align="center">
      <a href="https://one-step-admin.hurui.me" target="_blank">One-step-admin</a>
    </th>
  </tr>
  <tr>
    <th colspan="3" align="center">
      一款干啥都快人一步的 Vue 中后台管理系统框架
    </th>
  </tr>
  <tr>
    <td><img src="https://one-step-admin.hurui.me/preview1.png" /></td>
    <td><img src="https://one-step-admin.hurui.me/preview2.png" /></td>
    <td><img src="https://one-step-admin.hurui.me/preview3.png" /></td>
  </tr>
  <tr>
    <td><img src="https://one-step-admin.hurui.me/preview4.png" /></td>
    <td><img src="https://one-step-admin.hurui.me/preview5.png" /></td>
    <td><img src="https://one-step-admin.hurui.me/preview6.png" /></td>
  </tr>
</table>

<table>
  <tr>
    <th colspan="4" align="center">
      <a href="https://fantastic-mobile.hurui.me" target="_blank">Fantastic-mobile</a>
    </th>
  </tr>
  <tr>
    <th colspan="4" align="center">
      一款自成一派的移动端 H5 框架
    </th>
  </tr>
  <tr>
    <td><img src="https://fantastic-mobile.hurui.me/preview1.png" /></td>
    <td><img src="https://fantastic-mobile.hurui.me/preview2.png" /></td>
    <td><img src="https://fantastic-mobile.hurui.me/preview3.png" /></td>
    <td><img src="https://fantastic-mobile.hurui.me/preview4.png" /></td>
  </tr>
</table>
